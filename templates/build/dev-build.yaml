# File: templates/build/dev-build.yml
steps:

- task: NodeTool@0
  displayName: 'Use Node $(NODE_VERSION)'
  inputs:
    versionSpec: $(NODE_VERSION)

- task: Npm@1
  displayName: 'npm install'
  inputs:
    verbose: false

- task: Npm@1
  displayName: 'npm build'
  inputs:
    command: custom
    verbose: false
    customCommand: 'run build'

- task: PublishPipelineArtifact@1
  displayName: 'Publish Pipeline Artifact'
  inputs:
    artifact: $(ARTIFACT_NAME)

- task: SonarSource.sonarqube.15B84CA1-B62F-4A2A-A403-89B77A063157.SonarQubePrepare@4
  displayName: 'SonarQube: Analysis on SonarQube'
  inputs:
    SonarQube: SonarQube
    scannerMode: CLI
    configMode: manual
    projectKey: 'com.metlife'
    projectName: 'MXTREASURY'
    extraProperties: |
     # Additional properties that will be passed to the scanner, 
     # Put one key=value per line, example:
     sonar.exclusions=**/*.bin,**/*.js,**/*.html,**/*.css
     #sonar.coverage.exclusions=**/*
     sonar.coverage.exclusions=src/main/java/mx/com/metlife/*.java,src/main/java/mx/com/metlife/**/*.java,src/test/**/*.java
     sonar.java.binaries=build/classes/java/main
     sonar.java.libraries=dependencies/*.jar
     sonar.c.file.suffixes=-
     sonar.cpp.file.suffixes=-
     sonar.objc.file.suffixes=-
  enabled: false  

#- task: qetza.replacetokens.replacetokens-task.replacetokens@4
#  displayName: 'Replace image footer'
#  inputs:
#    rootDirectory: k8s
#    targetFiles: '$(System.DefaultWorkingDirectory)/server/cloud/Footer.tsx'
#    tokenPattern: doublebraces

#- script: |
#   cp $(System.DefaultWorkingDirectory)/server/cloud/Footer.tsx $(System.DefaultWorkingDirectory)/src/layouts/admin/components/Footer/
#   
#  displayName: 'Replace Footer to Release'

- task: SonarSource.sonarqube.6D01813A-9589-4B15-8491-8164AEB38055.SonarQubeAnalyze@4
  displayName: 'SonarQube: Run Code Analysis'
  enabled: false

- task: SonarSource.sonarqube.291ed61f-1ee4-45d3-b1b0-bf822d9095ef.SonarQubePublish@4
  displayName: 'SonarQube: Publish Quality Gate Result'
  enabled: false

- task: SimondeLang.sonar-buildbreaker.sonar-buildbreaker.sonar-buildbreaker@8
  displayName: 'SonarQube: Break build on quality gate failure'
  inputs:
    SonarQube: SonarQube
  enabled: false

- task: Veracode@3
  inputs:
    AnalysisService: 'Veracode'
    veracodeAppProfile: '14316_TREASURY'
    version: '$(ARTIFACT_NAME)-$(Build.Buildnumber)'
    sandboxName: 'treasury-ui'
    maximumWaitTime: '360'

#- task: Docker@2
#  displayName: login
#  inputs:
#    containerRegistry: $(SERVICE_CONNECTION_ACR)
#    command: login
- task: AzureCLI@2
  displayName: 'Login to ACR via ARM'
  inputs:
    azureSubscription: $(SERVICE_CONNECTION_ARM)
    scriptType: bash
    scriptLocation: inlineScript
    inlineScript: |
      az acr login --name $(ACR_LOGIN_SERVER) # ACR_LOGIN SERVER = name of your Azure Container Registry (example: "myacr.azurecr.io")

- script: |
    docker build -t $(ACR_LOGIN_SERVER).azurecr.io/$(ACR_REPOSITORY_UI):$(ACR_REPOSITORY_UI_TAG) -f Dockerfile .
    docker push $(ACR_LOGIN_SERVER).azurecr.io/$(ACR_REPOSITORY_UI):$(ACR_REPOSITORY_UI_TAG)
  displayName: "Build and Push Image"

#- task: Docker@2
#  displayName: 'Build and Push'
#  inputs:
#    command: buildAndPush
#    repository: $(ACR_REPOSITORY_UI)
#    Dockerfile: Dockerfile
#    tags: $(ACR_REPOSITORY_UI_TAG)
#    containerRegistry: $(SERVICE_CONNECTION_ACR)

#- task: Docker@2
#  displayName: logout
#  inputs:
#    containerRegistry: $(SERVICE_CONNECTION_ACR)
#    command: logout
- task: AzureCLI@2
  displayName: 'Logout from ACR'
  inputs:
    azureSubscription: $(SERVICE_CONNECTION_ARM)
    scriptType: bash
    scriptLocation: inlineScript
    inlineScript: |
      docker logout $(ACR_LOGIN_SERVER)


