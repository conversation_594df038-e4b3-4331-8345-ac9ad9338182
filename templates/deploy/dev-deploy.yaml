# PIP DEV Deploy pipeline
# File: cicd/build/deploy-dev.yml

steps:
  - task: AzureRmWebAppDeployment@4
    inputs:
      ConnectionType: 'AzureRM'
      azureSubscription: $(SERVICE_CONNECTION_ARM)
      appType: 'webAppContainer'
      WebAppName: '$(APP_SERVICE_NAME)'
      #deployToSlotOrASE: true
      ResourceGroupName: $(RG_NAME)
      #SlotName: 'dev'
      DockerNamespace: $(ACR_LOGIN_SERVER)
      DockerRepository: '$(ACR_REPOSITORY_UI)'
      DockerImageTag: '$(ACR_REPOSITORY_UI_TAG)'

  - task: AzureCLI@2
    inputs:
      azureSubscription: '$(SERVICE_CONNECTION_ARM)'
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        az webapp restart --name '$(APP_SERVICE_NAME)' --resource-group $(RG_NAME)