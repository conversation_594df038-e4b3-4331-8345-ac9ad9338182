# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
  branches:
    include:
      - release/dv
      - release/in
      - master
      - release/dr
resources:
  repositories:
    - repository: mxtreasury-devops
      type: git
      name: "MX FIN_CORP/mxtreasury-devops"
      ref: refs/heads/feature/veracode

variables:
  - group: 14316_CICD_CONF_DEV
  #  - group: 14316_CICD_CONF_INT
  #  - group: 14316_CICD_CONF_PROD
  #  - group: 14316_CICD_CONF_DR
  - name: sonarprojectkey
    value: 14316_Treasury_UI
  - name: sonarprojectname
    value: 14316_Treasury_UI
  - name: sonarSource
    value: src
  - name: javascriptlcovreportPaths
    value: coverage/lcov.info
  - name: VERACODE_PROJECT
    value: 14316_TREASURY-MX

stages:
  - stage: Build_DTR
    jobs:
      - job: DEV
        pool:
          name: "Azure-PROD-Windows"
        condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/release/dv'))
        steps:
          - template: /build-templates/build/dev-build.yaml
      # - job : INT
      #   pool:
      #     name: Linux
      #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/release/in'))
      #   steps:
      #    - template: /build-templates/build/int-build.yaml
      # - job : DRC
      #   pool:
      #     name: Linux
      #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/release/dr'))
      #   steps:
      #    - template : /build-templates/build/dr-build.yaml
      # - job : PROD
      #   pool:
      #     name: Linux
      #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
      #   steps:
      #    - template : /build-templates/build/prod-build.yaml

  - stage: Aprobacion_Dev
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/release/dv'))
    dependsOn: Build_DTR
    jobs:
      - deployment: ""
        environment: "14316-TREASURY-DEV-DEPLOY"

  - stage: Deploy_Dev
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/release/dv'))
    dependsOn: Aprobacion_Dev
    jobs:
      - job: Deploy
        pool:
          name: "Azure-PROD-Windows"
        steps:
          - template: /build-templates/deploy/dev-deploy.yaml

  # - stage : Aprobacion_INT
  #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/release/in'))
  #   dependsOn: Build_DTR
  #   jobs:
  #     - deployment: ''
  #       environment: '14316-TREASURY-INTEGRATION-DEPLOY'

  # - stage : Deploy_INT
  #   dependsOn: Aprobacion_INT
  #   jobs:
  #     - job : Deploy
  #       pool:
  #         name: Linux
  #       steps:
  #       - template : /build-templates/deploy/int-deploy.yaml

  # - stage : Aprobacion_DR_MetLife
  #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/release/dr'))
  #   dependsOn: Build_DTR
  #   jobs:
  #     - deployment: ''
  #       environment: '14316-TREASURY-DR-DEPLOY-METLIFE'

  # - stage : Aprobacion_DR_PM
  #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/release/dr'))
  #   dependsOn: Aprobacion_DR_MetLife
  #   jobs:
  #     - deployment: ''
  #       environment: '14316-TREASURY-DR-DEPLOY-PL'

  # - stage : Deploy_DR
  #   dependsOn: Aprobacion_DR_PM
  #   jobs:
  #     - deployment: ''
  #       environment: 'DEPLOY_DR_STAGE'
  #     - job : Deploy
  #       pool:
  #         name: Linux
  #       steps:
  #       - template : /build-templates/deploy/dr-deploy.yaml

  # - stage : Aprobacion_PR_MetLife
  #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
  #   dependsOn: Build_DTR
  #   jobs:
  #     - deployment: ''
  #       environment: '14316-TREASURY-PROD-DEPLOY-METLIFE'

  # - stage : Aprobacion_PR_PM
  #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
  #   dependsOn: Aprobacion_PR_MetLife
  #   jobs:
  #     - deployment: ''
  #       environment: '14316-TREASURY-PROD-DEPLOY-PL'

  # - stage : Deploy_Production
  #   dependsOn: Aprobacion_PR_PM
  #   jobs:
  #     - deployment: ''
  #       environment: 'DEPLOY_PROD_STAGE'
  #     - job : Deploy
  #       pool:
  #         name: Linux
  #       steps:
  #       - template : /build-templates/deploy/prod-deploy.yaml
