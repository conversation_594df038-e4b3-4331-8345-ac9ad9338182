import { createSlice } from "@reduxjs/toolkit";
const initialState = {
  isOpen: false,
  isOpen2: false,
  bank: "",
  account: "",
  initialDate: "",
  finalDate: "",
};
const modalSlice = createSlice({
  name: "modal",
  initialState,
  reducers: {
    openModal: (state) => {
      state.isOpen = true;
    },

    closeModal: (state) => {
      state.isOpen = false;
    },

    setBank: (state, action) => {
      state.bank = action.payload;
    },
    setAccount: (state, action) => {
      state.account = action.payload;
    },
    setInitialDate: (state, action) => {
      state.initialDate = action.payload;
    },
    setFinalDate: (state, action) => {
      state.finalDate = action.payload;
    },
    setIsOpen: (state, action) => {
      state.isOpen = action.payload;
    },
  },
});
export const {
  openModal,
  closeModal,
  setBank,
  setAccount,

  setFinalDate,
  setInitialDate,
  setIsOpen,
} = modalSlice.actions;
export default modalSlice.reducer;
