import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { mockColumns, mockData } from "./menuMockData";

type MenuProps = {};

const Menu: FC<MenuProps> = () => {

  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);
    
      const { result: responsePost } = usePost(callPostAPI);
      const { result, loading, error, refetch } = useGet<TableResponse>({
        url: "v1/services/treasury/menu-maintenance",
        call: true,
      });
      const onSaveMenu = (newElement: TableData) => {
        setCallPostAPI({
          url: "v1/services/treasury/menu-maintenance",
          call: true,
          data: newElement,
        });
      };
  
      useEffect(() => {
          if (responsePost != null) {
            refetch();
            setCallPostAPI(initialPostApi);
          }
        }, [responsePost, refetch]);
      
      if (loading)
          return (
            <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
          );
        if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE MENU</h1>
      <Table
        id="menu"
        data={result?.data ?? []}
        columns={mockColumns}
        filters={["tcmeCodModulo", "tcmeIdNivel", "tcmeNodoMaestro"]}
        onSave={onSaveMenu}
      />
    </div>
  );
};

export default Menu;
