import { FC, useState } from "react";
import "./ContaIngreso.css";
import { useNavigate } from "react-router-dom";
import DropDown from "../MetlifeComponents/DropDown/DropDown";
import DatePicker from "../MetlifeComponents/DatePicker/DatePicker";
import Button from "../MetlifeComponents/Button/Button";
import Modal from "../Estados/Modal/Modal";
import EstadoButtons from "../Estados/EstadoButtons/EstadoButtons";
import ContaButtons from "./ContaButtons/ContaButtons";
type ContaIngresosProps = {};

const ContaIngreso: FC<ContaIngresosProps> = () => {
  const navigate = useNavigate();
  const FileOnUpload = () => {};
  const DropDownBancosHandleClick = () => {};
  const DropDownCuentasHandleClick = () => {};
  const DropDownBancosHandleOnChange = (value: string) => {
    setBancoSeleccionado(value);
  };
  const DropDownCuentasHandleOnchange = () => {};
  const handleUpload = (file: File) => {};
  const DropDownBancosStyle = {
    maxWidth: "300px",
    width: "100%",
    marginTop: "1rem",
  };
  const DropDownCuentasStyle = {
    maxWidth: "300px",
    width: "100%",
    marginTop: "0.5rem",
  };

  const DropDownBancosOptions = [
    {
      ariaLabel: "BBVA",
      label: "BBVA",
      value: "BBVA",
    },
    {
      ariaLabel: "City",
      label: "City",
      value: "City",
    },
    {
      ariaLabel: "HSBC",
      label: "HSBC",
      value: "HSBC",
    },
    {
      ariaLabel: "JPMorgan",
      label: "JPMorgan",
      value: "JPMorgan",
    },
  ];
  const optionsR = [
    {
      label: "Formato Tradicional",
      value: "tradicional",
    },
    {
      label: "MT490",
      value: "MT490",
    },
  ];
  const radioStyle = {};
  const DropDownCuentasOptions = [
    {
      ariaLabel: "1234",
      label: "1234",
      value: "1234",
    },
    {
      ariaLabel: "2323232323",
      label: "2323232323",
      value: "2323232323",
    },
    {
      ariaLabel: "4545465464",
      label: "545465464",
      value: "545465464",
    },
  ];
  const ButtonProcesarStyle = {
    display: "flex",
  };
  const ButtonConsultarStyle = {
    display: "flex",
  };
  const ButtonSalirStyle = {
    display: "flex",
  };
  const handleOnClickConsultar = () => {
    setShowModal(true);
  };
  const handleOnClickSalir = () => {};
  const handleOnClickProcesar = () => {
    navigate("procesa");
  };
  const [showModal, setShowModal] = useState(false);
  const [fechaInicial, setFechaInicial] = useState("");
  const [fechaFinal, setFechaFinal] = useState("");
  return (
    <div className="conta-content">
      <h6 className="title">Contabilidad de ingresos</h6>
      <div className="dropdown-container">
        <div className="left-inputs">
          <div className="form-inline-bancos">
            <label htmlFor=" bancos"> Banco:</label>
            <DropDown
              change={DropDownBancosHandleOnChange}
              click={DropDownBancosHandleClick}
              opts={DropDownBancosOptions}
              style={DropDownBancosStyle}
              placeholder="Seleccione Banco"
              disabled={false}
            ></DropDown>
          </div>
          <div className="form-inline-cuentas">
            <label htmlFor="cuentas"> Cuenta:</label>
            <DropDown
              change={DropDownCuentasHandleOnchange}
              click={DropDownBancosHandleClick}
              style={DropDownCuentasStyle}
              opts={DropDownCuentasOptions}
              placeholder="Seleccione Cuenta"
              disabled={!setBancoSeleccionado}
            ></DropDown>
          </div>
        </div>
        <div className="right-inputs">
          <DatePicker
            id="fecha-inicial"
            label="FECHA DE MOVIMIENTOS"
            value={fechaInicial}
            onChange={(e) => setFechaInicial(e.target.value)}
            disabled={false}
            className=""
            style={{ width: "180px" }}
          ></DatePicker>
          <DatePicker
            id="fecha-final"
            label="FECHA CONTABLE"
            value={fechaFinal}
            onChange={(e) => setFechaFinal(e.target.value)}
            disabled={false}
            className=""
            style={{ width: "180px" }}
          ></DatePicker>
        </div>
      </div>
      <div className="button-wrapper">
        <ContaButtons></ContaButtons>
      </div>

      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",

          marginTop: "2rem",
          gap: "2rem",
          paddingLeft: "490px",
        }}
      ></div>

      <Modal
        isOpen={showModal}
        title="Generacion de Archivo"
        onClose={() => {
          setShowModal(false);
        }}
        onAcept={() => {}}
      >
        <div style={{ display: "flex", gap: "1rem" }}>
          <span style={{ color: "#005288", fontWeight: "500" }}>
            Archivo Enviado
          </span>
        </div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            gap: "1rem",
            marginTop: "1rem",
          }}
        >
          <span style={{ color: "#005288", fontWeight: "500" }}>
            Archivo GDL_M16_AAAAMMDDHHMSS.txt enviado de forma exitosa
          </span>
        </div>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "1rem",
            marginTop: "1.51rem",
          }}
        >
          <Button
            text="Aceptar"
            style={{}}
            funcionC={() => {
              // navigate("consulta");
              setShowModal(false);
            }}
          ></Button>
          <Button
            text="Salir"
            style={{}}
            funcionC={() => {
              setShowModal(false);
            }}
          ></Button>
        </div>
      </Modal>
    </div>
  );
};

export default ContaIngreso;
function setBancoSeleccionado(value: string) {
  throw new Error("Function not implemented.");
}
