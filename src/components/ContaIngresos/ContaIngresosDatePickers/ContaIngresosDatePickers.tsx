import React, { useState } from "react";
import "./DatePickers.css";
import DatePicker from "../../MetlifeComponents/DatePicker/DatePicker";
type ContaIngresosDatePickersProps = {};
const ContaIngresosDatePickers: React.FC<
  ContaIngresosDatePickersProps
> = () => {
  const [fechaInicial, setFechaInicial] = useState("");
  const [fechaFinal, setFechaFinal] = useState("");
  return (
    <div className="date-picker">
      <DatePicker
        id="fecha-inicial"
        label="FECHA DE MOVIMIENTOS"
        value={fechaInicial}
        onChange={(e) => setFechaInicial(e.target.value)}
        disabled={false}
        className=""
      ></DatePicker>
      <DatePicker
        id="fecha-final"
        label="FECHA CONTABLE"
        value={fechaFinal}
        onChange={(e) => setFechaFinal(e.target.value)}
        disabled={false}
        className=""
      ></DatePicker>
    </div>
  );
};
export default ContaIngresosDatePickers;
