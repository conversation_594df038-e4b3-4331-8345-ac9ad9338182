import React from "react";
import "./ContaButtons.css";
import Button from "../../MetlifeComponents/Button/Button";
type ContaButtonsProps = {};
const ContaButtons: React.FC = () => {
  return (
    <div className="button-wrapper">
      <Button
        text="Buscar"
        funcionC={() => {
          /* setShowModal(true);
                      //navigate("consulta"); */
        }}
        style={undefined}
      ></Button>
      ,
      <Button
        text="Procesar"
        funcionC={() => {
          /*setShowModal(true);
                      //navigate("consulta"); */
        }}
        style={undefined}
      ></Button>
      ,
      <Button
        text="Consultar"
        funcionC={() => {
          /* setShowModal(true);
                      //navigate("consulta");*/
        }}
        style={undefined}
      ></Button>
      ,
      <Button
        text="Salir"
        style={{ minWidth: "140px", padding: "10px 0" }}
        funcionC={() => {
          /*
              setShowModal(false); */
        }}
      ></Button>
      ,
    </div>
  );
};
export default ContaButtons;
