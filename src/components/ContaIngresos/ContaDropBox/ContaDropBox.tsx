import "./ContaDropBox.css";
import React, { useState } from "react";
import DropDown from "../../MetlifeComponents/DropDown/DropDown";
type ContaDropBoxProps = {};
const ContaDropBox: React.FC<ContaDropBoxProps> = () => {
  const [BancoSeleccionado, setBancoSeleccionado] = useState("");
  const DropDownBancosStyle = {
    maxWidth: "300px",
    width: "100%",
    marginTop: "1rem",
  };
  const DropDownCuentasStyle = {
    maxWidth: "300px",
    width: "100%",
    marginTop: "0.5rem",
  };

  const DropDownBancosOptions = [
    {
      ariaLabel: "BBVA",
      label: "BBVA",
      value: "BBVA",
    },
    {
      ariaLabel: "City",
      label: "City",
      value: "City",
    },
    {
      ariaLabel: "HSBC",
      label: "HSBC",
      value: "HSBC",
    },
    {
      ariaLabel: "JPMorgan",
      label: "JPMorgan",
      value: "JPMorgan",
    },
  ];
  const optionsR = [
    {
      label: "Formato Tradicional",
      value: "tradicional",
    },
    {
      label: "MT490",
      value: "MT490",
    },
  ];
  const DropDownCuentasOptions = [
    {
      ariaLabel: "1234",
      label: "1234",
      value: "1234",
    },
    {
      ariaLabel: "2323232323",
      label: "2323232323",
      value: "2323232323",
    },
    {
      ariaLabel: "4545465464",
      label: "545465464",
      value: "545465464",
    },
  ];
  const DropDownCuentasHandleOnchange = () => {};
  const handleUpload = (file: File) => {};
  const DropDownBancosHandleClick = () => {};
  const DropDownCuentasHandleClick = () => {};
  const DropDownBancosHandleOnChange = (value: string) => {
    setBancoSeleccionado(value);
  };
  return (
    <div className="drop-row">
      <div className="form-inline-bancos">
        <label htmlFor=" bancos"> Banco:</label>
        <DropDown
          change={DropDownBancosHandleOnChange}
          click={DropDownBancosHandleClick}
          opts={DropDownBancosOptions}
          style={DropDownBancosStyle}
          placeholder="Seleccione Banco"
          disabled={false}
        ></DropDown>
        <span></span>
      </div>
      <div className="form-inline-cuentas">
        <label htmlFor="cuentas"> Cuenta:</label>
        <DropDown
          change={DropDownCuentasHandleOnchange}
          click={DropDownBancosHandleClick}
          style={DropDownCuentasStyle}
          opts={DropDownCuentasOptions}
          placeholder="Seleccione Cuenta"
          disabled={!setBancoSeleccionado}
        ></DropDown>
      </div>
    </div>
  );
};
export default ContaDropBox;
