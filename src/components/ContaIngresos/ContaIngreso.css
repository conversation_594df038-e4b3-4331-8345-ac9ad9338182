.conta-page {
  display: flex;
  flex-direction: column;

  font-family: "Inter", "sans-serifS";
  width: 100%;
}

.conta-content {
  display: flex;
  flex-direction: column;
  align-items: center;

  font-family: "Inter", "sans-serifS";
  width: 100%;
}

.title-conatiner {
  display: flex;
  padding: 1rem;
  align-items: center;
  justify-content: center;
}
.main {
  display: flex;
  width: 100%;
}
.title {
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.title {
  text-align: center;

  font-size: 1.8rem;
  margin-bottom: 2rem;
}

.dropdown-container {
  display: flex;

  gap: 2rem;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 0 2rem;
  width: 100%;
}

.title {
  text-align: center;

  gap: 1rem;
  font-size: 2rem;
  font-weight: bold;
  width: 100%;
  margin-top: 3rem;
}

.left-inputs {
  display: flex;

  flex: 1;
  justify-content: start;
}
.right-inputs {
  display: flex;

  flex: 1;
  justify-content: flex-start;
  align-items: flex-end;
  width: 100%;
  gap: 2rem;
}
.button-wrapper {
  display: flex;
  width: 100%;
  margin-left: 15rem;
  align-items: center;
  margin-top: 30rem;
}
