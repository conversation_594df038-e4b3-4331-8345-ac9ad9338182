import React, { useState, useRef } from "react";
import { CloudUpload } from "react-bootstrap-icons";
import "./FileUploader.css";
type Props = {
  label: string;
  allowedTypes?: string[];
  maxSizeMb?: number;
  onupload: (file: File) => void;
  className?: string;
  icon?: React.ReactNode;
  id: string;
  text?: string;
};
const FileUploader: React.FC<Props> = ({
  allowedTypes,
  maxSizeMb = 10,
  onupload,
  label = "Cargar Archivo",
  className = "",
  icon,
  id,
  text,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<String | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const handleFile = (selectedFile: File | null) => {
    if (!selectedFile) {
      return;
    }
    if (
      allowedTypes &&
      !allowedTypes.some((ext) =>
        selectedFile.name.toLocaleLowerCase().endsWith(ext.toLocaleLowerCase()),
      )
    ) {
      setError("Extensiónde archivo no permitido");
      return;
    }
    if (selectedFile?.size > maxSizeMb * 1024 * 1024) {
      setError("Archivo demasiado grande");
      return;
    }
    setError(null);
    setFile(selectedFile);
    onupload(selectedFile);
  };
  return (
    <div className={"file-uploader${className}"}>
      <button style={{ borderRadius: "8px" }} className="file-upload-button">
        <div className="file-label-row">
          <div
            className="file-icon"
            onClick={() => {
              inputRef.current?.click();
            }}
          >
            {icon || <CloudUpload size={20} color="#0d6efd" />}
            <span> {text ?? "Seleccionar Archivo"}</span>
          </div>
        </div>
      </button>

      <input
        type="file"
        id={id}
        ref={inputRef}
        style={{ display: "none" }}
        onChange={(e) => handleFile(e.target.files?.[0] || null)}
      />
      {file && (
        <p className="file-name">
          Archivo:
          <strong>{file.name}</strong>
        </p>
      )}
      {error && <p className="file-error">{error}</p>}
    </div>
  );
};
export default FileUploader;
