import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "CODIGO",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-2",
    label: "CLAVE",
    accessor: "TCEF_ID_ENTIDAD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "NOMBRE",
    accessor: "TCEF_NOMBRE",
    isLink: false,
    sortable: true,
    hidden: true,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-4",
    label: "NOMBRE",
    accessor: "TCEF_NOMBRE_CORTO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-5",
    label: "TIPO",
    accessor: "TCEF_TIPO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-5",
    label: "OPERACION",
    accessor: "TCEF_OPERACION",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-6",
    label: "CODIGO SWIFT",
    accessor: "TCEF_COD_SWIFT",
    isLink: false,
    sortable: true,
    hidden: true,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-7",
    label: "DOMICILIO",
    accessor: "TCEF_DOMICILIO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-8",
    label: "CONTACTO",
    accessor: "TCEF_EJE_CONTACTO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-9",
    label: "TELEFONO",
    accessor: "TCEF_TELEFONOS",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-10",
    label: "USUARIO CREO",
    accessor: "TCEF_USER_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-11",
    label: "FECHA CREO",
    accessor: "TCEF_FEC_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
  {
    id: "table-head-12",
    label: "USUARIO MOD",
    accessor: "TCEF_USER_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-13",
    label: "FECHA MOD",
    accessor: "TCEF_FEC_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
];

const mockData: TableData[] = [
  {
    id: "1",
    TCEF_ID_ENTIDAD: "BNM",
    TCEF_NOMBRE_CORTO: "Banamex",
    TCEF_TIPO: "Banco",
    TCEF_OPERACION: "Egresos",
    TCEF_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCEF_EJE_CONTACTO: "Pedro Gonzalez",
    TCEF_TELEFONOS: "************",
    TCEF_USER_CREO: "user",
    TCEF_FEC_CREO: "2025-03-27",
    TCEF_USER_MOD: null,
    TCEF_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "2",
    TCEF_ID_ENTIDAD: "BBA",
    TCEF_NOMBRE_CORTO: "Bancomer",
    TCEF_TIPO: "Banco",
    TCEF_OPERACION: "Egresos",
    TCEF_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCEF_EJE_CONTACTO: "Maria Rodriguez",
    TCEF_TELEFONOS: "************",
    TCEF_USER_CREO: "user",
    TCEF_FEC_CREO: "2025-03-27",
    TCEF_USER_MOD: null,
    TCEF_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-2",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
];

export { mockData, mockColumns };
