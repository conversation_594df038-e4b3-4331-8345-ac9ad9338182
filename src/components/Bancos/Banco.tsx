import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";

type BancoProps = {};

const URL_BANK = "v1/services/treasury/catalogs/banks";

const Banco: FC<BancoProps> = () => {
  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);

  const { result: responsePost } = usePost(callPostAPI);
  const { result, loading, error, refetch } = useGet<TableResponse>({
    url: URL_BANK,
    call: true,
  });

  const onSaveBanco = (newElement: TableData) => {
    setCallPostAPI({
      url: URL_BANK,
      call: true,
      data: newElement,
    });
  };

  const onEditBanco = (editElement: TableData) => {
    setCallPostAPI({
      url: `${URL_BANK}/${editElement?.id}`,
      call: true,
      method: "PUT",
      data: editElement,
    });
  };

  const onDeleteBanco = (elementId: number) => {
    setCallPostAPI({
      url: `${URL_BANK}/${elementId}`,
      call: true,
      method: "DELETE",
    });
  };

  useEffect(() => {
    if (responsePost != null) {
      refetch();
      setCallPostAPI(initialPostApi);
    }
  }, [responsePost, refetch]);

  if (loading)
    return (
      <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
    );
  if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE BANCOS</h1>
      <Table
        id="bancos"
        data={result?.data ?? []}
        columns={result?.columns ?? []}
        filters={["id", "tcef_ID_ENTIDAD", "tcef_NOMBRE_CORTO"]}
        onSave={onSaveBanco}
        onEdit={onEditBanco}
        onDelete={onDeleteBanco}
      />
    </div>
  );
};

export default Banco;
