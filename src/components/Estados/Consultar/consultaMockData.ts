import { TableInputType } from "../../../enums";
import {
  TableColumn,
  TableData,
} from "../../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "ID",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-1",
    label: "FECHA",
    accessor: "FECHA",
    isLink: false,
    sortable: true,
    hidden: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-2",
    label: "REFERENCIA",
    accessor: "REFERENCIA",
    isLink: false,
    sortable: true,
    hidden: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "CONCEPTO",
    accessor: "CONCEPTO",
    isLink: false,
    sortable: true,
    hidden: false,

    type: TableInputType.TEXT,
  },
  {
    id: "table-head-4",
    label: "IMPORTE",
    accessor: "IMPORTE",
    isLink: false,
    sortable: true,
    hidden: false,
    isTotal: true,

    type: TableInputType.TEXT,
  },
];

const mockData: TableData[] = [
  {
    id: 1,

    FECHA: "2023-02-10",
    REFERENCIA: "GH7282223273273283273723712732737",
    CONCEPTO: "Pago Proveedor",
    IMPORTE: "$34535.00",
  },
  {
    id: 2,
    FECHA: "2023-02-10",
    REFERENCIA: "GH7282223273273283273723712732737",
    CONCEPTO: "Pago Proveedor",
    IMPORTE: "$34535.00",
  },
  {
    id: 3,
    FECHA: "2023-02-10",
    REFERENCIA: "GH7282223273273283273723712732737",
    CONCEPTO: "Pago Proveedor",
    IMPORTE: "$34535.00",
  },
  {
    id: 4,
    FECHA: "2023-02-10",
    REFERENCIA: "GH7282223273273283273723712732737",
    CONCEPTO: "Pago Proveedor",
    IMPORTE: "$34535.00",
  },
  {
    id: 5,
    FECHA: "2023-02-10",
    REFERENCIA: "GH7282223273273283273723712732737",
    CONCEPTO: "Pago Proveedor",
    IMPORTE: "$34535.00",
  },
  {
    id: 6,
    FECHA: "2023-02-10",
    REFERENCIA: "GH7282223273273283273723712732737",
    CONCEPTO: "Pago Proveedor",
    IMPORTE: "$34535.00",
  },
  {
    id: 7,
    FECHA: "2023-02-10",
    REFERENCIA: "GH7282223273273283273723712732737",
    CONCEPTO: "Pago Proveedor",
    IMPORTE: "$34535.00",
  },
  {
    id: 8,
    FECHA: "2023-02-10",
    REFERENCIA: "GH7282223273273283273723712732737",
    CONCEPTO: "Pago Proveedor",
    IMPORTE: "$34535.00",
  },
];

export { mockData, mockColumns };
