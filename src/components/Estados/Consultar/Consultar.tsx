import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../../hooks/useFetch";
import Table from "../../MetlifeComponents/Table/Table";
import {
  TableData,
  TableResponse,
} from "../../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { mockColumns, mockData } from "./consultaMockData";
import DropDown from "../../MetlifeComponents/DropDown/DropDown";
import "./Consultar.css";
import Button from "../../MetlifeComponents/Button/Button";
import DatePicker from "../../MetlifeComponents/DatePicker/DatePicker";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { openModal } from "../../../redux/modalSlice";
import { data, useNavigate } from "react-router-dom";
import Modal from "../Modal/Modal";
import ConsultarButtons from "./ConsultarEstadoButtons/ConsultarEstado.Buttons";
import ConsultaDatePicker from "./ConsultarEstadoDatePickers/ConsultaDatePickers";
import { DropDownOption } from "@components/MetlifeComponents/DropDown/DropDownOption";
import ConsultarModal from "../ConsultarModal/ConsultarModal";

type ConsultaProps = {};

const FileOnUpload = () => {};

const DropDownBancosStyle = {
  maxWidth: "300px",
  width: "100%",
  marginTop: "1rem",
};
const DropDownCuentasStyle = {
  maxWidth: "300px",
  width: "100%",
  marginTop: "0.5rem",
};

const DropDownBancosOptions = [
  {
    ariaLabel: "BBVA",
    label: "BBVA",
    value: "BBVA",
  },
  {
    ariaLabel: "City",
    label: "City",
    value: "City",
  },
  {
    ariaLabel: "HSBC",
    label: "HSBC",
    value: "HSBC",
  },
  {
    ariaLabel: "JPMorgan",
    label: "JPMorgan",
    value: "JPMorgan",
  },
];
const optionsR = [
  {
    label: "Formato Tradicional",
    value: "tradicional",
  },
  {
    label: "MT490",
    value: "MT490",
  },
];

const radioStyle = {};
const DropDownCuentasOptions = [
  {
    ariaLabel: "1234",
    label: "1234",
    value: "1234",
  },
  {
    ariaLabel: "**********",
    label: "**********",
    value: "**********",
  },
  {
    ariaLabel: "4*********",
    label: "*********",
    value: "*********",
  },
];

const Consulta: FC<ConsultaProps> = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isOpenState2 = useSelector((state: RootState) => state.modal.isOpen2);
  const bancoStateValue = useSelector((state: RootState) => state.modal.bank);
  const cuentaStateValue = useSelector(
    (state: RootState) => state.modal.account
  );
  const initialDate = useSelector(
    (state: RootState) => state.modal.initialDate
  );
  const finalDate = useSelector((state: RootState) => state.modal.finalDate);

  const [myData, setMyData] = useState(mockData);
  const ButtonProcesarStyle = {
    display: "flex",
  };
  const ButtonConsultarStyle = {
    display: "flex",
  };
  const ButtonSalirStyle = {
    display: "flex",
  };

  const handleOnClickProcesar = () => {};
  const handleOnClickConsultar = () => {
    dispatch(openModal());
  };
  const handleOnClickSalir = () => {};

  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);

  const { result: responsePost } = usePost(callPostAPI);
  const { result, loading, error, refetch } = useGet<TableResponse>({
    url: "https://api.example.com/",
    call: false, //true TODO: until have the BE
  });
  // console.log({ result, loading, error });

  const onDeleteCuenta = (elementId: number) => {
    const updatedData = myData.filter((item) => item.id !== elementId);
    setMyData(updatedData);

    /*setCallPostAPI({
      url: `https://api.example.com/${elementId}`,
      call: true,
      method: "DELETE",
    }); */
  };

  useEffect(() => {
    if (responsePost != null) {
      refetch();
      setCallPostAPI(initialPostApi);
    }
  }, [responsePost, refetch]);

  if (loading)
    return (
      <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
    );
  if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <div className="title-container">
        <h1 className="title">MANTENIMIENTO DE ESTADOS</h1>
      </div>
      <div className="met-dropdowns">
        <div className="form-inline-bancos">
          <label htmlFor=" bancos"> Banco:</label>
          <DropDown
            selectedValue={bancoStateValue}
            opts={DropDownBancosOptions}
            style={DropDownBancosStyle}
            placeholder="Seleccione Banco"
            disabled={true}
          ></DropDown>
        </div>
        <div className="form-inline-cuentas">
          <label htmlFor="cuentas"> Cuenta:</label>
          <DropDown
            selectedValue={cuentaStateValue}
            style={DropDownCuentasStyle}
            opts={DropDownCuentasOptions}
            placeholder="Seleccione Cuenta"
            disabled={true}
          ></DropDown>
        </div>
        <div>
          <input
            type="text"
            value={initialDate}
            disabled
            style={{ width: "180px", height: "60px" }}
          />
        </div>
        <div>
          <input
            type="text"
            value={finalDate}
            disabled
            style={{ width: "180px", height: "60px" }}
          />
        </div>
      </div>

      <Table
        id="consulta"
        // data={result?.data ?? []}
        // columns={result?.columns ?? []}
        data={myData}
        columns={mockColumns}
        //onSave={onSaveCompania}
        // onEdit={onEditCompania}
        onDelete={onDeleteCuenta}
        enableAddRow={false}
        filters={["FECHA", "id", "REFERENCIA", "CONCEPTO"]}
      />
      <div className="buttons-wrapper">
        <Button
          text="Procesar"
          funcionC={handleOnClickProcesar}
          disabled={true}
        ></Button>
        ,<Button text="Consultar" funcionC={handleOnClickConsultar}></Button>,
        <Button text="Salir" funcionC={handleOnClickSalir}></Button>
      </div>
      <ConsultarModal></ConsultarModal>
    </div>
  );
};

export default Consulta;
