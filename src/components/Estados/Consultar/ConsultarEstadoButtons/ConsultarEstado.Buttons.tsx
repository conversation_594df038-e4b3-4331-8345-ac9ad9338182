import React from "react";

import { RootState } from "../../../../redux/store";
import { closeModal, setIsOpen } from "../../../../redux/modalSlice";
import Button from "../../../MetlifeComponents/Button/Button";
import "./ConsultarEstadoButtons.css";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
type ConsultarButtonsProps = {};

const ConsultarButtons: React.FC<ConsultarButtonsProps> = ({}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const aceptarOnClick = () => {
    navigate("/consulta", { replace: true });
    dispatch(closeModal());
  };
  const salirOnclick = () => {
    dispatch(closeModal());
  };
  return (
    <div className="button-container">
      <Button text="Aceptar" funcionC={aceptarOnClick}></Button>

      <Button text="Salir" funcionC={salirOnclick}></Button>
    </div>
  );
};
export default ConsultarButtons;
