import React from "react";
import { useDispatch, useSelector } from "react-redux";
import "./ConsultaDatePickers.css";
import DatePicker from "../../../MetlifeComponents/DatePicker/DatePicker";
import { RootState } from "../../../../redux/store";

import { setFinalDate, setInitialDate } from "../../../../redux/modalSlice";
type ConsultaDatePickerProps = {};
const ConsultaDatePicker: React.FC<ConsultaDatePickerProps> = () => {
  const dispatch = useDispatch();

  const fechaInicialHandler = (
    initialDate: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setInitialDate(initialDate.target.value));
  };
  const fechaFinalHandler = (
    finalDate: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setFinalDate(finalDate.target.value));
  };
  const dateI = useSelector((state: RootState) => state.modal.initialDate);
  const dateF = useSelector((state: RootState) => state.modal.finalDate);

  return (
    <div className="datepickers-container">
      <DatePicker
        id="fecha-inicial"
        label="FECHA INICIAL"
        value={dateI}
        onChange={fechaInicialHandler}
        disabled={false}
        className=""
        style={{ width: "180px" }}
      ></DatePicker>
      <DatePicker
        id="fecha-final"
        label="FECHA FINAL"
        value={dateF}
        onChange={fechaFinalHandler}
        disabled={false}
        className=""
        style={{ width: "180px" }}
      ></DatePicker>
    </div>
  );
};
export default ConsultaDatePicker;
