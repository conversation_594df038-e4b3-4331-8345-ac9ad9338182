import React, { useState } from "react";
import "./EstadoDropDown.css";
import DropDown from "../../MetlifeComponents/DropDown/DropDown";
import FileUploader from "../../FileUploader/FileUploader";
import { DropDownOption } from "../../MetlifeComponents/DropDown/DropDownOption";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import {
  setBank,
  setAccount,
  openModal,
  closeModal,
} from "../../../redux/modalSlice";
type DropDownRowProps = {};
const EstadoDropDowns: React.FC<DropDownRowProps> = ({}) => {
  const dispatch = useDispatch();
  const bancoState = useSelector((state: RootState) => state.modal.bank);
  const cuentaState = useSelector((state: RootState) => state.modal.account);

  const DropDownBancosHandleClick = () => {};
  const DropDownCuentasHandleClick = () => {};
  const DropDownBancosHandleOnChange = (banco: DropDownOption) => {
    dispatch(setBank(banco.value));
  };
  const DropDownCuentasHandleOnchange = (cuenta: DropDownOption) => {
    dispatch(setAccount(cuenta.value));
  };
  const handleUpload = (file: File) => {};
  const DropDownBancosStyle = {
    width: "225.29px",
    height: "50px",
    marginTop: "1rem",
  };
  const DropDownCuentasStyle = {
    width: "225.29x",
    height: "50px",
    marginTop: "0.5rem",
  };

  const DropDownBancosOptions = [
    {
      ariaLabel: "BBVA",
      label: "BBVA",
      value: "BBVA",
    },
    {
      ariaLabel: "City",
      label: "City",
      value: "City",
    },
    {
      ariaLabel: "HSBC",
      label: "HSBC",
      value: "HSBC",
    },
    {
      ariaLabel: "JPMorgan",
      label: "JPMorgan",
      value: "JPMorgan",
    },
  ];
  const [CuentaSeleccionada, setCuentaSeleccionada] = useState("");
  const [BancoSeleccionado, setBancoSeleccionado] = useState("");
  const FileOnUpload = () => {};
  const DropDownCuentasOptions = [
    {
      ariaLabel: "1234",
      label: "1234",
      value: "1234",
    },
    {
      ariaLabel: "**********",
      label: "**********",
      value: "**********",
    },
    {
      ariaLabel: "4545465464",
      label: "545465464",
      value: "545465464",
    },
  ];
  return (
    <div className="main">
      <div className="bloque-superior">
        <div className="dropdown-container">
          <div className="file-upload-wrapper">
            <FileUploader
              id="file-uploader"
              onupload={handleUpload}
              label="Cargar Estado de Cuenta"
              allowedTypes={[".csv", ".pdf", ".txt"]}
              maxSizeMb={50}
              text="Cargar estado de cuenta"
            ></FileUploader>
          </div>
          <div className="form-inline-bancos">
            <label htmlFor=" bancos"> Banco:</label>
            <DropDown
              change={DropDownBancosHandleOnChange}
              click={DropDownBancosHandleClick}
              opts={DropDownBancosOptions}
              style={DropDownBancosStyle}
              placeholder="Seleccione Banco"
              disabled={false}
              selectedValue={bancoState}
            ></DropDown>
          </div>
          <div className="form-inline-cuentas">
            <label htmlFor="cuentas"> Cuenta:</label>
            <DropDown
              change={DropDownCuentasHandleOnchange}
              click={DropDownBancosHandleClick}
              style={DropDownCuentasStyle}
              opts={DropDownCuentasOptions}
              placeholder="Seleccione Cuenta"
              disabled={bancoState === ""}
              selectedValue={cuentaState}
            ></DropDown>
          </div>
        </div>
      </div>
    </div>
  );
};
export default EstadoDropDowns;
