import React from "react";
import "./EstadoButtons.css";
import Button from "../../MetlifeComponents/Button/Button";
import { openModal } from "../../../redux/modalSlice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@redux/store";
type EstadoButtonsProps = {};

const EstadoButtons: React.FC<EstadoButtonsProps> = ({}) => {
  const dispatch = useDispatch();
  const handleOnClickConsultar = () => {
    dispatch(openModal());
  };
  const bancoStateValue = useSelector((state: RootState) => state.modal.bank);
  const cuentaStateValue = useSelector(
    (state: RootState) => state.modal.account
  );
  const handleOnClickSalir = () => {};
  const handleOnClickProcesar = () => {};
  return (
    <div className="buttons-wrapper">
      <div
        className="buttons-container"
        style={{ display: "flex", justifyContent: "center" }}
      >
        <Button text="Procesar" funcionC={handleOnClickProcesar}></Button>,
        <Button
          text="Consultar"
          funcionC={handleOnClickConsultar}
          disabled={bancoStateValue === "" || cuentaStateValue == ""}
        ></Button>
        ,<Button text="Salir" funcionC={handleOnClickSalir}></Button>
      </div>
    </div>
  );
};
export default EstadoButtons;
