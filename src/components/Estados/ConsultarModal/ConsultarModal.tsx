import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import { RootState } from "../.././../redux/store";
import {
  closeModal,
  openModal,
  setAccount,
  setBank,
} from "../.././../redux/modalSlice";
import ConsultarButtons from "../Consultar/ConsultarEstadoButtons/ConsultarEstado.Buttons";
import ConsultaDatePicker from "../Consultar/ConsultarEstadoDatePickers/ConsultaDatePickers";
import { useNavigate } from "react-router-dom";
import Modal from "../Modal/Modal";
import DropDown from "../../MetlifeComponents/DropDown/DropDown";
import { DropDownOption } from "@components/MetlifeComponents/DropDown/DropDownOption";
type ConsultarModalProps = {};
const ConsultarModal: React.FC<ConsultarModalProps> = ({}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const FileOnUpload = () => {};
  const isOpenState = useSelector((state: RootState) => state.modal.isOpen);
  const bancoStateValue = useSelector((state: RootState) => state.modal.bank);
  const cuentaStateValue = useSelector(
    (state: RootState) => state.modal.account
  );
  const DropDownBancosHandleClick = () => {};
  const DropDownCuentasHandleClick = () => {};

  const DropDownCuentasHandleOnchange = (account: DropDownOption) => {
    dispatch(setAccount(account.value));
  };
  const DropDownBankHandleOnChange = (bank: DropDownOption) => {
    dispatch(setBank(bank.value));
  };
  const handleUpload = (file: File) => {};
  const DropDownBancosStyle = {
    width: "225.29px",
    height: "50px",
    marginTop: "1rem",
  };
  const DropDownCuentasStyle = {
    width: "225.29x",
    height: "50px",
    marginTop: "0.5rem",
  };

  const DropDownBancosOptions = [
    {
      ariaLabel: "BBVA",
      label: "BBVA",
      value: "BBVA",
    },
    {
      ariaLabel: "City",
      label: "City",
      value: "City",
    },
    {
      ariaLabel: "HSBC",
      label: "HSBC",
      value: "HSBC",
    },
    {
      ariaLabel: "JPMorgan",
      label: "JPMorgan",
      value: "JPMorgan",
    },
  ];
  const optionsR = [
    {
      label: "Formato Tradicional",
      value: "tradicional",
    },
    {
      label: "MT490",
      value: "MT490",
    },
  ];
  const radioStyle = { display: "flex", TextWrap: "nowrap" };
  const DropDownCuentasOptions = [
    {
      ariaLabel: "1234",
      label: "1234",
      value: "1234",
    },
    {
      ariaLabel: "**********",
      label: "**********",
      value: "**********",
    },
    {
      ariaLabel: "4*********",
      label: "*********",
      value: "*********",
    },
  ];

  return (
    <div className="consultar-modal-container">
      <Modal
        isOpen={isOpenState}
        title="Consultar"
        onClose={() => {}}
        onAcept={() => {}}
      >
        <div style={{ display: "flex", gap: "1rem" }}>
          <DropDown
            opts={DropDownBancosOptions}
            change={DropDownBankHandleOnChange}
            click={() => {}}
            placeholder="Bancos"
            selectedValue={bancoStateValue}
            disabled={false}
            style={{ width: "280px" }}
          ></DropDown>
          <DropDown
            opts={DropDownCuentasOptions}
            change={DropDownCuentasHandleOnchange}
            click={() => {}}
            placeholder="Cuentas"
            selectedValue={cuentaStateValue}
            disabled={false}
            style={{ width: "280px" }}
          ></DropDown>
        </div>
        <ConsultaDatePicker></ConsultaDatePicker>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "1rem",
            marginTop: "1.51rem",
          }}
        ></div>
        <ConsultarButtons></ConsultarButtons>
      </Modal>
    </div>
  );
};
export default ConsultarModal;
