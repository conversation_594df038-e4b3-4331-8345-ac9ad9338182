import { FC, useState } from "react";
import "./Estado.css";
import DropDown from "../MetlifeComponents/DropDown/DropDown";
import Button from "../MetlifeComponents/Button/Button";
import FileUploader from "../FileUploader/FileUploader";
import CheckBox from "../MetlifeComponents/SingleCheckBox/SingleCheckBox";
import RadioButton from "../MetlifeComponents/RadioButton/RadioButton";
import { RadioButtonOption } from "../MetlifeComponents/RadioButton/RadioButtonOption";
import { click } from "@testing-library/user-event/dist/click";
import Modal from "./Modal/Modal";
import DatePicker from "../MetlifeComponents/DatePicker/DatePicker";
import { useNavigate } from "react-router-dom";
import { TextWrap } from "react-bootstrap-icons";
import EstadoButtons from "./EstadoButtons/EstadoButtons";
import EstadoDropDowns from "./EstadoDropDowns/EstadoDropDowns";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { closeModal } from "../../redux/modalSlice";
import ConsultarButtons from "./Consultar/ConsultarEstadoButtons/ConsultarEstado.Buttons";
import ConsultaDatePicker from "./Consultar/ConsultarEstadoDatePickers/ConsultaDatePickers";
import ConsultarModal from "./ConsultarModal/ConsultarModal";
type EstadoProps = {};

const Estado = () => {
  return (
    <div className="estado-page">
      <h6 className="title">Mantenimiento de Estados de Cuenta</h6>
      <div className="estado-content">
        <EstadoDropDowns></EstadoDropDowns>
      </div>
      <div className="button-wrapper">
        <EstadoButtons></EstadoButtons>
      </div>
      <ConsultarModal></ConsultarModal>
    </div>
  );
};

export default Estado;
