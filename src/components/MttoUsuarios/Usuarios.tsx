import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { mockColumns, mockData } from "./usuariosMockData";

type CompaniaProps = {};

const Usuarios: FC<CompaniaProps> = () => {

  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);
      
    const { result: responsePost } = usePost(callPostAPI);
    const { result, loading, error, refetch } = useGet<TableResponse>({
      url: "v1/services/treasury/user-maintenance",
      call: true,
    });

    useEffect(() => {
      if (responsePost != null) {
        refetch();
        setCallPostAPI(initialPostApi);
      }
    }, [responsePost, refetch]);
  
    if (loading)
      return (
        <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
      );
    if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE USUARIOS</h1>
      <Table
        id="menu"
        data={mockData}
        columns={mockColumns}
        filters={["TCME_COD_MODULO", "TCME_ID_NIVEL", "TCME_DESCRIPCION"]}
      />
    </div>
  );
};

export default Usuarios;
