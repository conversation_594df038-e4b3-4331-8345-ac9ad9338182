import { Facebook } from "react-bootstrap-icons";
import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "ID",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: true,
    editable: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-2",
    label: "COMPAÑIA",
    accessor: "tctaCveCia",
    isLink: false,
    sortable: true,
    hidden: true,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "BANCO",
    accessor: "tctaIdEntidad",
    isLink: false,
    sortable: true,
    hidden: true,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-4",
    label: "MONEDA",
    accessor: "tcefCveMoneda",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-5",
    label: "NUMERO CUENTA",
    accessor: "tcefNumCuenta",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-6",
    label: "CUARTO NIVEL CONTABLE",
    accessor: "tctaAuxCont",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-7",
    label: "NUMERO CHEQUE",
    accessor: "tctaNumCheque",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-8",
    label: "SUCURSAL",
    accessor: "tctaIdSucursal",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-9",
    label: "DIGITO VERIFICADOR",
    accessor: "tctaCalDigito",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-10",
    label: "PROTECCION CHEQUE",
    accessor: "tctaProtCke",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  
  {
    id: "table-head-11",
    label: "USUARIO CREO",
    accessor: "tctaUserCreo",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-12",
    label: "FECHA CREO",
    accessor: "tctaFecCreo",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE_TIME,
  },
  {
    id: "table-head-13",
    label: "USUARIO MOD",
    accessor: "tctaUserMod",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-14",
    label: "FECHA MOD",
    accessor: "tctaFecMod",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE_TIME,
  },
];

const mockData: TableData[] = [
  {
    ID: "1",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "2338008",
    TCTA_AUX_CONT: "423",
    TCTA_NUM_CHEQUE: "7878534",
    TCTA_ID_SUCURSAL: "519990029",
    TCTA_CAL_DIGITO: "0023",
    TCTA_PROT_CKE: "S",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: null,
    TCTA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "1",
  },
  {
    ID: "2",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "0188014191",
    TCTA_AUX_CONT: "436",
    TCTA_NUM_CHEQUE: "6373",
    TCTA_ID_SUCURSAL: "511150179",
    TCTA_CAL_DIGITO: "",
    TCTA_PROT_CKE: "",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: "USER_MOD",
    TCTA_FEC_MOD: "15/04/2025",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "2",
  },
  {
    ID: "3",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "2338016",
    TCTA_AUX_CONT: "444",
    TCTA_NUM_CHEQUE: "",
    TCTA_ID_SUCURSAL: "",
    TCTA_CAL_DIGITO: "",
    TCTA_PROT_CKE: "",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: null,
    TCTA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "3",
  },
  /*
  
  
  
  
  
  
  
  */
];

export { mockData, mockColumns };
