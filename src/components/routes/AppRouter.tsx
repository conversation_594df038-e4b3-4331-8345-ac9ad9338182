import { Routes, Route } from "react-router-dom";
import Compania from "../Companias/Compania";
import Home from "../Home/Home";
import { FC } from "react";
import Banco from "../Bancos/Banco";
import Cuentas from "../MttoCuentas/Cuentas";
import Usuarios from "../MttoUsuarios/Usuarios";
import Concepto from "../Conceptos/Concepto";
import Plantilla from "../Plantillas/Plantilla";
import Estado from "../Estados/Estado";
import ContaIngreso from "../ContaIngresos/ContaIngreso";
import Rol from "../Rol/Rol";
import Menu from "../MttoMenu/Menu";
import MenuRol from "../MenuRol/MenuRol";
import MttoConcept from "../MttoConcept/MttoConcept";
import MttoAccTemplate from "../MttoAccTemplate/MttoAccTemplate";
import Consulta from "../Estados/Consultar/Consultar";
import Procesa from "../Estados/Procesar/Procesar";

const AppRouter: FC = () => {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/mantenimiento-companias" element={<Compania />} />
      <Route path="/mantenimiento-bancos" element={<Banco />} />
      <Route path="/mantenimiento-cuenta" element={<Cuentas />} />
      <Route path="/mantenimiento-usuario" element={<Usuarios />} />
      <Route path="/mantenimiento-concepto" element={<MttoConcept />} />
      <Route path="/mantenimiento-plantilla" element={<Plantilla />} />
      <Route path="/mantenimiento-estado" element={<Estado />} />
      <Route path="/consulta" element={<Consulta />} />
      <Route path="/mantenimiento-estado/procesa" element={<Procesa />} />

      <Route path="/contabilizacion" element={<ContaIngreso />} />
      <Route path="/rol" element={<Rol />} />
      <Route path="/menu-rol" element={<MenuRol />} />
      <Route path="/menu" element={<Menu />} />
      <Route
        path="/mantenimiento-plantillas-contables"
        element={<MttoAccTemplate />}
      />
    </Routes>
  );
};
export default AppRouter;
