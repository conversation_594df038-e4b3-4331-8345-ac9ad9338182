import { FC, useState, useEffect } from "react";
import "./transferList.css";

// Define the structure for tree items
interface TreeItem {
  id: string;
  name: string;
  roleKey?: string;
  module?: string;
  descripcion?: string;
  children?: TreeItem[];
}

// Props for the TransferList component
interface TransferListProps {
  leftTitle?: string;
  rightTitle?: string;
  roleKeyFilter?: string;
  moduleFilter?: string;
  descripcionFilter?: string;
}

const TransferList: FC<TransferListProps> = ({
  leftTitle = "Lista Izquierda",
  rightTitle = "Lista Derecha",
  roleKeyFilter = "",
  moduleFilter = "",
  descripcionFilter = "",
}) => {
  // Sample initial data for left list (available menus)
  const initialLeftItems: TreeItem[] = [
    {
      id: "1",
      name: "Gestión de Ingresos",
      roleKey: "admin",
      module: "I",
      descripcion: "Módulo principal para administrar y gestionar todos los procesos relacionados con ingresos financieros",
      children: [
        { id: "1-1", name: "Reportes de Ingresos", roleKey: "admin", module: "I", descripcion: "Generación de reportes detallados sobre ingresos y estadísticas financieras" },
        { id: "1-2", name: "Análisis de Cobranza", roleKey: "contador", module: "I", descripcion: "Herramientas para analizar patrones de cobranza y recuperación de cartera" },
        { id: "1-3", name: "Presupuesto de Ingresos", roleKey: "supervisor", module: "I", descripcion: "Planificación y control presupuestario de ingresos proyectados" },
      ],
    },
    {
      id: "2",
      name: "Operaciones de Ingresos",
      roleKey: "cajero",
      module: "I",
      descripcion: "Funciones operativas diarias para el manejo y registro de ingresos",
      children: [
        { id: "2-1", name: "Registro de Cobros", roleKey: "cajero", module: "I", descripcion: "Sistema para registrar y procesar cobros de clientes y pagos recibidos" },
        { id: "2-2", name: "Consultas de Ingresos", roleKey: "cajero", module: "I", descripcion: "Consulta de historial y estado de ingresos registrados en el sistema" },
        { id: "2-3", name: "Conciliación de Ingresos", roleKey: "supervisor", module: "I", descripcion: "Proceso de conciliación bancaria y validación de ingresos" },
      ],
    },
    {
      id: "3",
      name: "Gestión de Egresos",
      roleKey: "admin",
      module: "E",
      descripcion: "Módulo principal para administrar y controlar todos los egresos y gastos empresariales",
      children: [
        { id: "3-1", name: "Reportes de Egresos", roleKey: "admin", module: "E", descripcion: "Generación de reportes de gastos y análisis de egresos por categoría" },
        { id: "3-2", name: "Control de Gastos", roleKey: "contador", module: "E", descripcion: "Monitoreo y control de gastos operativos y administrativos" },
        { id: "3-3", name: "Presupuesto de Egresos", roleKey: "supervisor", module: "E", descripcion: "Planificación presupuestaria y control de gastos proyectados" },
      ],
    },
    {
      id: "4",
      name: "Operaciones de Egresos",
      roleKey: "cajero",
      module: "E",
      descripcion: "Operaciones diarias para el procesamiento y autorización de pagos y egresos",
      children: [
        { id: "4-1", name: "Registro de Pagos", roleKey: "cajero", module: "E", descripcion: "Sistema para registrar y procesar pagos a proveedores y terceros" },
        { id: "4-2", name: "Consultas de Egresos", roleKey: "cajero", module: "E", descripcion: "Consulta de historial y estado de egresos y pagos realizados" },
        { id: "4-3", name: "Autorización de Pagos", roleKey: "supervisor", module: "E", descripcion: "Proceso de autorización y aprobación de pagos de alto monto" },
      ],
    },
  ];

  // Function to get initial right items based on filters
  const getInitialRightItems = (module: string, role: string): TreeItem[] => {
    // Complete sample assignments data structure
    const allAssignments: TreeItem[] = [
      // Ingresos - Admin
      {
        id: "assigned-i-admin-1",
        name: "Administración Completa - Ingresos",
        roleKey: "admin",
        module: "I",
        descripcion: "Acceso completo a todas las funciones administrativas del módulo de ingresos",
        children: [
          { id: "assigned-i-admin-1-1", name: "Mantenimiento de Compañías", roleKey: "admin", module: "I", descripcion: "Gestión y configuración de empresas en el sistema" },
          { id: "assigned-i-admin-1-2", name: "Mantenimiento de Usuarios", roleKey: "admin", module: "I", descripcion: "Administración de cuentas de usuario y permisos" },
          { id: "assigned-i-admin-1-3", name: "Configuración de Ingresos", roleKey: "admin", module: "I", descripcion: "Configuración de parámetros y reglas de negocio para ingresos" },
        ],
      },
      // Ingresos - Cajero
      {
        id: "assigned-i-cajero-1",
        name: "Operaciones Básicas - Ingresos",
        roleKey: "cajero",
        module: "I",
        descripcion: "Funciones operativas básicas para el manejo diario de ingresos",
        children: [
          { id: "assigned-i-cajero-1-1", name: "Registro de Cobros", roleKey: "cajero", module: "I", descripcion: "Captura y procesamiento de cobros recibidos" },
          { id: "assigned-i-cajero-1-2", name: "Consultas de Ingresos", roleKey: "cajero", module: "I", descripcion: "Consulta de transacciones y movimientos de ingresos" },
        ],
      },
      // Ingresos - Supervisor
      {
        id: "assigned-i-supervisor-1",
        name: "Supervisión de Ingresos",
        roleKey: "supervisor",
        module: "I",
        descripcion: "Herramientas de supervisión y control para el módulo de ingresos",
        children: [
          { id: "assigned-i-supervisor-1-1", name: "Autorización de Ingresos", roleKey: "supervisor", module: "I", descripcion: "Aprobación y autorización de ingresos de alto monto" },
          { id: "assigned-i-supervisor-1-2", name: "Reportes de Supervisión", roleKey: "supervisor", module: "I", descripcion: "Reportes especializados para supervisión y control" },
        ],
      },
      // Egresos - Admin
      {
        id: "assigned-e-admin-1",
        name: "Administración Completa - Egresos",
        roleKey: "admin",
        module: "E",
        descripcion: "Acceso completo a todas las funciones administrativas del módulo de egresos",
        children: [
          { id: "assigned-e-admin-1-1", name: "Mantenimiento de Proveedores", roleKey: "admin", module: "E", descripcion: "Gestión del catálogo de proveedores y terceros" },
          { id: "assigned-e-admin-1-2", name: "Mantenimiento de Conceptos de Pago", roleKey: "admin", module: "E", descripcion: "Configuración de conceptos y categorías de pagos" },
          { id: "assigned-e-admin-1-3", name: "Configuración de Egresos", roleKey: "admin", module: "E", descripcion: "Configuración de parámetros y reglas de negocio para egresos" },
        ],
      },
      // Egresos - Cajero
      {
        id: "assigned-e-cajero-1",
        name: "Operaciones Básicas - Egresos",
        roleKey: "cajero",
        module: "E",
        descripcion: "Funciones operativas básicas para el manejo diario de egresos",
        children: [
          { id: "assigned-e-cajero-1-1", name: "Registro de Pagos", roleKey: "cajero", module: "E", descripcion: "Captura y procesamiento de pagos a realizar" },
          { id: "assigned-e-cajero-1-2", name: "Consultas de Egresos", roleKey: "cajero", module: "E", descripcion: "Consulta de transacciones y movimientos de egresos" },
        ],
      },
      // Egresos - Contador
      {
        id: "assigned-e-contador-1",
        name: "Contabilidad de Egresos",
        roleKey: "contador",
        module: "E",
        descripcion: "Herramientas contables especializadas para el control de egresos",
        children: [
          { id: "assigned-e-contador-1-1", name: "Conciliación de Pagos", roleKey: "contador", module: "E", descripcion: "Proceso de conciliación bancaria de pagos realizados" },
          { id: "assigned-e-contador-1-2", name: "Reportes Contables", roleKey: "contador", module: "E", descripcion: "Generación de reportes contables y financieros" },
        ],
      },
    ];

    // If no filters are applied, return all items
    if ((!module || module === "") && (!role || role === "")) {
      return allAssignments;
    }

    // Filter by module and/or role
    return allAssignments.filter(item => {
      const moduleMatch = !module || module === "" || item.module === module;
      const roleMatch = !role || role === "" || item.roleKey === role;
      return moduleMatch && roleMatch;
    });
  };

  // Sample initial data for right list (assigned menus)
  const initialRightItems: TreeItem[] = getInitialRightItems(moduleFilter, roleKeyFilter);

  // Helper function to filter left items by description
  const filterLeftItemsByDescription = (items: TreeItem[], filter: string): TreeItem[] => {
    if (!filter || filter === "") {
      return items;
    }

    const filterRecursive = (items: TreeItem[]): TreeItem[] => {
      return items.reduce((filtered: TreeItem[], item) => {
        // Check if current item matches the filter
        const itemMatches = item.descripcion?.toLowerCase().includes(filter.toLowerCase()) || false;

        // Filter children recursively
        const filteredChildren = item.children ? filterRecursive(item.children) : [];

        // Include item if it matches or has matching children
        if (itemMatches || filteredChildren.length > 0) {
          filtered.push({
            ...item,
            children: filteredChildren.length > 0 ? filteredChildren : item.children
          });
        }

        return filtered;
      }, []);
    };

    return filterRecursive(items);
  };

  // Get filtered left items based on description filter
  const getFilteredLeftItems = () => {
    return filterLeftItemsByDescription(initialLeftItems, descripcionFilter);
  };

  // State for both lists
  const [leftItems, setLeftItems] = useState<TreeItem[]>(getFilteredLeftItems());
  const [rightItems, setRightItems] = useState<TreeItem[]>(initialRightItems);

  // State for selected items
  const [selectedLeftItem, setSelectedLeftItem] = useState<{
    item: TreeItem | null;
    path: string[];
  }>({ item: null, path: [] });
  const [selectedRightItem, setSelectedRightItem] = useState<{
    item: TreeItem | null;
    path: string[];
  }>({ item: null, path: [] });

  // Update right items when filters change
  useEffect(() => {
    const newRightItems = getInitialRightItems(moduleFilter, roleKeyFilter);
    setRightItems(newRightItems);
    // Clear selections when filters change
    setSelectedRightItem({item: null, path: []});
  }, [moduleFilter, roleKeyFilter]);

  // Update left items when description filter changes
  useEffect(() => {
    const newLeftItems = filterLeftItemsByDescription(initialLeftItems, descripcionFilter);
    setLeftItems(newLeftItems);
    // Clear left selection when filter changes
    setSelectedLeftItem({item: null, path: []});
  }, [descripcionFilter, initialLeftItems]);



  // Helper function to find an item by path
  const findItemByPath = (
    items: TreeItem[],
    path: string[],
  ): TreeItem | null => {
    if (path.length === 0) return null;

    const currentId = path[0];
    const item = items.find((item) => item.id === currentId);

    if (!item) return null;

    if (path.length === 1) return item;

    if (!item.children) return null;

    return findItemByPath(item.children, path.slice(1));
  };

  // Helper function to remove an item by path
  const removeItemByPath = (items: TreeItem[], path: string[]): TreeItem[] => {
    if (path.length === 0) return items;

    const currentId = path[0];

    if (path.length === 1) {
      return items.filter((item) => item.id !== currentId);
    }

    const itemIndex = items.findIndex((item) => item.id === currentId);

    if (itemIndex === -1 || !items[itemIndex].children) return items;

    const updatedItems = [...items];
    updatedItems[itemIndex] = {
      ...updatedItems[itemIndex],
      children: removeItemByPath(
        updatedItems[itemIndex].children || [],
        path.slice(1),
      ),
    };

    return updatedItems;
  };

  // Helper function to add an item to a specific parent
  const addItemToParent = (
    items: TreeItem[],
    parentPath: string[],
    newItem: TreeItem,
  ): TreeItem[] => {
    if (parentPath.length === 0) {
      return [...items, newItem];
    }

    const currentId = parentPath[0];
    const itemIndex = items.findIndex((item) => item.id === currentId);

    if (itemIndex === -1) return items;

    const updatedItems = [...items];

    if (parentPath.length === 1) {
      // Add to this parent's children
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        children: [...(updatedItems[itemIndex].children || []), newItem],
      };
    } else {
      // Continue traversing
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        children: addItemToParent(
          updatedItems[itemIndex].children || [],
          parentPath.slice(1),
          newItem,
        ),
      };
    }

    return updatedItems;
  };

  // Move selected item from left to right
  const moveSelectedToRight = () => {
    if (!selectedLeftItem.item) return;

    // Clone the item to move
    const itemToMove = { ...selectedLeftItem.item };

    // Remove from left list
    const newLeftItems = removeItemByPath(leftItems, selectedLeftItem.path);

    // Add to right list (either as root or as child of selected right item)
    let newRightItems;
    if (selectedRightItem.item) {
      newRightItems = addItemToParent(
        rightItems,
        selectedRightItem.path,
        itemToMove,
      );
    } else {
      newRightItems = [...rightItems, itemToMove];
    }

    // Update state
    setLeftItems(newLeftItems);
    setRightItems(newRightItems);
    setSelectedLeftItem({ item: null, path: [] });
  };

  // Move selected item from right to left
  const moveSelectedToLeft = () => {
    if (!selectedRightItem.item) return;

    // Clone the item to move
    const itemToMove = { ...selectedRightItem.item };

    // Remove from right list
    const newRightItems = removeItemByPath(rightItems, selectedRightItem.path);

    // Add to left list (either as root or as child of selected left item)
    let newLeftItems;
    if (selectedLeftItem.item) {
      newLeftItems = addItemToParent(
        leftItems,
        selectedLeftItem.path,
        itemToMove,
      );
    } else {
      newLeftItems = [...leftItems, itemToMove];
    }

    // Update state
    setRightItems(newRightItems);
    setLeftItems(newLeftItems);
    setSelectedRightItem({ item: null, path: [] });
  };

  // Move all items from left to right
  const moveAllToRight = () => {
    // If a right item is selected, add all left items as its children
    if (selectedRightItem.item) {
      let newRightItems = [...rightItems];

      // Add each left item as a child of the selected right item
      leftItems.forEach((item) => {
        newRightItems = addItemToParent(newRightItems, selectedRightItem.path, {
          ...item,
        });
      });

      setRightItems(newRightItems);
    } else {
      // Otherwise, just append all left items to the right list
      setRightItems([...rightItems, ...leftItems.map((item) => ({ ...item }))]);
    }

    // Clear left list
    setLeftItems([]);
    setSelectedLeftItem({ item: null, path: [] });
  };

  // Move all items from right to left
  const moveAllToLeft = () => {
    // If a left item is selected, add all right items as its children
    if (selectedLeftItem.item) {
      let newLeftItems = [...leftItems];

      // Add each right item as a child of the selected left item
      rightItems.forEach((item) => {
        newLeftItems = addItemToParent(newLeftItems, selectedLeftItem.path, {
          ...item,
        });
      });

      setLeftItems(newLeftItems);
    } else {
      // Otherwise, just append all right items to the left list
      setLeftItems([...leftItems, ...rightItems.map((item) => ({ ...item }))]);
    }

    // Clear right list
    setRightItems([]);
    setSelectedRightItem({ item: null, path: [] });
  };

  // Recursive function to render tree items
  const renderTreeItems = (
    items: TreeItem[],
    onSelect: (item: TreeItem, path: string[]) => void,
    selectedItem: { item: TreeItem | null; path: string[] },
    currentPath: string[] = [],
  ) => {
    return (
      <ul className="transfer-list-items">
        {items.map((item) => {
          const itemPath = [...currentPath, item.id];
          const isSelected =
            selectedItem.item &&
            selectedItem.path.join("-") === itemPath.join("-");

          return (
            <li
              key={item.id}
              className={`transfer-list-item ${isSelected ? "selected" : ""}`}
              onClick={(e) => {
                e.stopPropagation();
                onSelect(item, itemPath);
              }}
            >
              <div className="item-content">
                <span>{item.name}</span>
              </div>

              {item.children &&
                item.children.length > 0 &&
                renderTreeItems(
                  item.children,
                  onSelect,
                  selectedItem,
                  itemPath,
                )}
            </li>
          );
        })}
      </ul>
    );
  };

  return (
    <div className="container">
      <div className="transfer-list-container">
        {/* Left List */}
        <div className="transfer-list-box">
          <h3 className="transfer-list-title">{leftTitle}</h3>
          <div className="transfer-list-content">
            {leftItems.length > 0 ? (
              renderTreeItems(
                leftItems,
                (item, path) => setSelectedLeftItem({ item, path }),
                selectedLeftItem,
              )
            ) : (
              <div className="empty-list">No hay elementos</div>
            )}
          </div>
        </div>

        {/* Transfer Buttons */}
        <div className="transfer-buttons">
          <button
            className="transfer-button"
            onClick={moveSelectedToRight}
            disabled={!selectedLeftItem.item}
            title="Mover elemento seleccionado a la derecha"
          >
            <span>&#8250;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveAllToRight}
            disabled={leftItems.length === 0}
            title="Mover todos los elementos a la derecha"
          >
            <span>&#187;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveSelectedToLeft}
            disabled={!selectedRightItem.item}
            title="Mover elemento seleccionado a la izquierda"
          >
            <span>&#8249;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveAllToLeft}
            disabled={rightItems.length === 0}
            title="Mover todos los elementos a la izquierda"
          >
            <span>&#171;</span>
          </button>
        </div>

        {/* Right List */}
        <div className="transfer-list-box">
          <h3 className="transfer-list-title">{rightTitle}</h3>
          <div className="transfer-list-content">
            {rightItems.length > 0 ? (
              renderTreeItems(
                rightItems,
                (item, path) => setSelectedRightItem({ item, path }),
                selectedRightItem,
              )
            ) : (
              <div className="empty-list">
                {(moduleFilter || roleKeyFilter) ?
                  `No hay elementos para los filtros seleccionados` :
                  "No hay elementos"
                }
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransferList;
