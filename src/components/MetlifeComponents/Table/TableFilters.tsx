import { FC, useEffect, useState } from "react";
import { XCircleFill } from "react-bootstrap-icons";
import MLDCReactInputText from "@metlife/mldc-react-input-text";
import MLDCReactInputNumber from "@metlife/mldc-react-input-number";
import DatePicker from "../DatePicker/DatePicker";
import { TableColumn, TableData } from "./table-types";
import { TableInputType } from "../../../enums";
import { allEmptyValues } from "../../../utils/objectHelper";

type TableFiltersProps = {
  data: TableData[];
  filters: string[];
  columns: TableColumn[];
  onFilterChange: (filteredData: TableData[]) => void;
};

type MyObject = {
  [key: string]: any;
};

const TableFilters: FC<TableFiltersProps> = ({
  data,
  columns,
  filters,
  onFilterChange,
}) => {
  const [filterData, setFilterData] = useState<MyObject | null>(null);

  const disableIfEmpty = filterData === null;

  const resetFilters = () => {
    setFilterData(null);
    onFilterChange(data);
  };

  const onInputFilterChange = (name: string, value: any) => {
    setFilterData({ ...filterData, [name]: value.target.value });
  };

  const renderInputByType = (col: TableColumn) => {
    const commonProps = {
      id: `filter-${col.id}`,
      label: col.label,
      fullWidth: false,
      fullWithOnMobile: false,
      value: filterData?.[col.accessor],
      key: `filter-key-${col.accessor}-${col.id}`,
      onChange: (value: any) => onInputFilterChange(col.accessor, value),
    };
    switch (col.type) {
      case TableInputType.NUMBER:
        return <MLDCReactInputNumber {...commonProps} />;
      case TableInputType.DATE:
        return <DatePicker {...commonProps} />;
      default:
        return <MLDCReactInputText {...commonProps} />;
    }
  };

  const renderInputsByColumns = () =>
    columns
      .filter((col) => filters.includes(col.accessor))
      .map((col) => renderInputByType(col));

  const applyFilters = (filterData: MyObject | null) => {
    if (filterData !== null) {
      const filtersToApply = Object.keys(filterData).filter(
        (f) => filterData?.[f] !== "",
      );
      const funcFiltersToApply = filtersToApply.map(
        (f) => (d: TableData) => d?.[f]?.toString().includes(filterData?.[f]),
      );
      const filteredResult = funcFiltersToApply.reduce(
        (d, f) => d.filter(f),
        data,
      );
      onFilterChange(filteredResult);
    }
  };

  useEffect(() => {
    if (allEmptyValues(filterData)) {
      resetFilters();
    } else {
      applyFilters(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterData]);

  return (
    <>
      <div className="table-header">
        {renderInputsByColumns()}
        <div
          className={`button-clear-filters ${disableIfEmpty ? "disable" : ""}`}
          onClick={resetFilters}
        >
          <XCircleFill color="var(--brand-primary-ml-blue3)" size={40} />
        </div>
      </div>
    </>
  );
};
export default TableFilters;
