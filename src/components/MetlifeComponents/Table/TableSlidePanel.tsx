import { FC } from "react";
import MLDCReactSlidePanel from "@metlife/mldc-react-slide-panel";
import MLDCReactInputText from "@metlife/mldc-react-input-text";
import MLDCReactInputNumber from "@metlife/mldc-react-input-number";
import MLDCReactDropdown from "@metlife/mldc-react-dropdown";
import DatePicker from "../DatePicker/DatePicker";
import { TableColumn, TableData } from "./table-types";
import { TableInputType } from "../../../enums";

type TableSlidePanelProps = {
  data?: TableData;
  columns: TableColumn[];
  onSave: () => void;
  openPanel: boolean;
  isEditingRow: boolean;
  setOpenPanel: (open: boolean) => void;
};

const TableSlidePanel: FC<TableSlidePanelProps> = ({
  data,
  columns,
  onSave,
  openPanel,
  setOpenPanel,
  isEditingRow,
}) => {
  const renderInputByType = (col: TableColumn) => {
    const commonProps = {
      fullWidth: true,
      label: col.label,
      id: col.id,
      disabled: !col.editable,
      className: "col-lg-12 col-md-12 col-sm-12",
      value: isEditingRow ? (data?.[col.accessor]?.toString() ?? "") : "",
    };

    switch (col.type) {
      case TableInputType.NUMBER:
        return <MLDCReactInputNumber {...commonProps} />;
      case TableInputType.DATE:
        return <DatePicker {...commonProps} />;
      case TableInputType.SELECT:
        return (
          <MLDCReactDropdown
            {...commonProps}
            placeholder={col.label}
            options={col.displayOptions ?? []}
            selectedValue={
              isEditingRow ? (data?.[col.accessor]?.toString() ?? "") : ""
            }
          />
        );
      default:
        return <MLDCReactInputText {...commonProps} />;
    }
  };

  const renderInputsByColumns = () => (
    <div style={{ padding: "24px 16px" }}>
      {columns.map((col) => (
        <div
          key={`key-${col.accessor}-${col.id}`}
          className="row"
          style={{ marginTop: "12px" }}
        >
          {renderInputByType(col)}
        </div>
      ))}
    </div>
  );

  return (
    <MLDCReactSlidePanel
      id="table-slide-panel"
      hidden={!openPanel}
      singleColumnWidth={true}
      title={!isEditingRow ? "AGREGAR" : "EDITAR"}
      primaryAction={() => onSave()}
      primaryButtonText="Guardar"
      secondaryAction={() => setOpenPanel(false)}
      secondaryButtonText="Cancelar"
      closeAction={(_, hideModal) => {
        setOpenPanel(hideModal);
      }}
      triggerId="trigger-button"
      bodyContent={openPanel ? renderInputsByColumns() : null}
    />
  );
};
export default TableSlidePanel;
