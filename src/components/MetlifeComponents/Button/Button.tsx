import MLDCReactButton from "@metlife/mldc-react-button";
import "./Button.css";
type Props = {
  text?: string;
  style?: any;
  funcionC?: any;
  disabled?: any;
};
const Button = ({ text, style, funcionC, disabled }: Props) => {
  return (
    <div style={{ minWidth: "130px" }}>
      <MLDCReactButton
        className="mldc-button"
        disabled={disabled}
        extraProps={{}}
        id="primary-button-id1"
        onClick={funcionC}
        onKeyDown={function noRefCheck() {}}
        text={text}
      />
    </div>
  );
};
export default Button;
