import { ChangeEvent, FC, useEffect, useState } from "react";

type DatePickerProps = {
  id: string;
  value: string;
  label?: string;
  disabled?: boolean;
  className?: string;
  style?: any;
  type?: "date" | "datetime-local";
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
};

const DatePicker: FC<DatePickerProps> = ({
  id,
  label = "FECHA",
  value,
  disabled = false,
  className,
  onChange,
  style,
  type = "date",
}) => {
  const [dateValue, setDateValue] = useState<string>(value);
  const [isMyInputFocused, setIsMyInputFocused] = useState(false);

  const onInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (disabled) {
      return;
    }
    setDateValue(e.target.value);
    if (onChange) {
      onChange(e);
    }
  };

  useEffect(() => {
    const g = document.getElementById(`${id}-group`) as HTMLInputElement;
    const i = document.getElementById(`${id}-input`) as HTMLInputElement;
    const l = document.getElementById(`${id}-label`) as HTMLInputElement;
    if (isMyInputFocused) {
      g?.classList?.add("focused");
    } else {
      g?.classList?.remove("focused");
    }
    if (isMyInputFocused || dateValue !== "") {
      i?.setAttribute("data-class", "shown");
      if (!l.classList.contains("up")) {
        l?.classList?.remove("down");
        l?.classList?.add("up");
      }
    } else if (dateValue === "") {
      i?.removeAttribute("data-class");
      l?.classList?.remove("up");
      l?.classList?.add("down");
    }
  }, [id, isMyInputFocused, dateValue]);

  return (
    <div
      style={style}
      id={id}
      onBlur={() => setIsMyInputFocused(false)}
      onFocus={() => setIsMyInputFocused(true)}
      className={`mldc-input-date-picker-container full-width full-with-on-mobile ${className}`}
    >
      <div id={`${id}-group`} className="mldc-input-date-picker-group outline">
        <div className="input-group with-icon" tabIndex={-1}>
          <label id={`${id}-label`}>
            <span>{label}</span>
          </label>
          <input
            type={type}
            id={`${id}-input`}
            value={dateValue}
            disabled={disabled}
            onChange={onInputChange}
          />
        </div>
      </div>
    </div>
  );
};
export default DatePicker;
