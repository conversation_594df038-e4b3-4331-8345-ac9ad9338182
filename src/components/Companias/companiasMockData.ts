import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "ID",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-2",
    label: "NOMBRE",
    accessor: "TCIA_NOMBRE",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "RFC",
    accessor: "TCIA_RFC",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-4",
    label: "AGRUPADOR",
    accessor: "TCIA_REG_AGRUPADOR",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-5",
    label: "DOM<PERSON><PERSON><PERSON>",
    accessor: "TCIA_DOMICILIO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-6",
    label: "FECHA",
    accessor: "TCIA_FEC_INI_OPERACION",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.DATE,
  },
  {
    id: "table-head-7",
    label: "USUARIO CREO",
    accessor: "TCIA_USER_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-8",
    label: "FECHA CREO",
    accessor: "TCIA_FEC_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
  {
    id: "table-head-9",
    label: "USUARIO MOD",
    accessor: "TCIA_USER_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-10",
    label: "FECHA MOD",
    accessor: "TCIA_FEC_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
];

const mockData: TableData[] = [
  {
    ID: "1",
    TCIA_NOMBRE: "Metlife Mexico, S.A. de C.V.",
    TCIA_RFC: "MME-920427-EM3",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "1992-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "1",
  },
  {
    ID: "2",
    TCIA_NOMBRE: "Serv Admon Gral, S.A. de C.V.",
    TCIA_RFC: "SAG-961022-ET9",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "2003-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-2",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "2",
  },
  {
    ID: "3",
    TCIA_NOMBRE: "Metlife Mexico Servicios, S.A.",
    TCIA_RFC: "MMS-020926-3I4",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "1992-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "3",
  },
  {
    ID: "4",
    TCIA_NOMBRE: "Metlife Pensiones Mexico, S.A.",
    TCIA_RFC: "MPM-020731-CB0",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "1997-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "4",
  },
  {
    ID: "5",
    TCIA_NOMBRE: "MLA Servicios, S.A. de C.V.",
    TCIA_RFC: "MSE-041108-1N1",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "2005-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "5",
  },
  {
    ID: "6",
    TCIA_NOMBRE: "MLA Comercial, S.A. de C.V.",
    TCIA_RFC: "MCO-041108-C2A",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "2005-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "6",
  },
  {
    ID: "7",
    TCIA_NOMBRE: "Metlife Mexico Cares, S.A. de C.V.",
    TCIA_RFC: "MMC-040910-LS1",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "2005-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "7",
  },
  {
    ID: "8",
    TCIA_NOMBRE: "Funcdacion Metlife Mexico, A.C.",
    TCIA_RFC: "FMM-040910-2I9",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "2005-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "8",
  },
  {
    ID: "9",
    TCIA_NOMBRE: "ML Capacitacion Comercial, S.A.",
    TCIA_RFC: "MCC-080207-JK1",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "2009-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "9",
  },
  {
    ID: "10",
    TCIA_NOMBRE: "Metlife LA Aces. e Inv. LTD",
    TCIA_RFC: "MLA-020930-ID2",
    TCIA_REG_AGRUPADOR: "METLIFE",
    TCIA_DOMICILIO: "Bvld A Camacho 32 LChapultepec",
    TCIA_FEC_INI_OPERACION: "2009-01-01",
    TCIA_USER_CREO: "user",
    TCIA_FEC_CREO: "2025-03-27",
    TCIA_USER_MOD: null,
    TCIA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "10",
  },
];

export { mockData, mockColumns };
